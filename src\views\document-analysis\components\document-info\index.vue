<template>
  <div class="document-info-box"> 
    <div class="file-info">
      <a-button>
        <template #icon>
          <MenuUnfoldOutlined class="doc-icon"/>
        </template>
      </a-button>
      <svg-icon :icon="getFileIcon()" class="file-icon"/>
      <span class="file-name">{{ documentTitle }}</span>
      <div class="file-details-box">
        <div class="file-details">
          最新修改<span class="num">3.5</span>个，
          新增中<span class="num">1</span>个，
          删除中<span class="num">1</span>个，数据失效：<span class="num error">2</span>个
        </div>
        <a-button class="btn" type="link" @click="handleRetry">失败重试</a-button>
      </div>
    </div>
    <div class="action-buttons">
      <a-button  @click="handlePrev">上一个</a-button>
      <a-button  @click="handleNext">下一个</a-button>
      <span class="divider">/</span>
      <div class="base-btn-gradient" @click="handleCheck">审核通过</div>
      <a-button @click="handleAllCheck">全部审核通过</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MenuUnfoldOutlined } from '@ant-design/icons-vue'
import { getFileIcon } from '@/views/hooks/use-tool'

defineOptions({ name: 'DocumentInfo' })

interface Props {
  documentTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  documentTitle: '中华人民共和国政府采购法'
})

const emits = defineEmits(['prev', 'next', 'check', 'all-check'])

const handlePrev = () => {
  emits('prev')
}

const handleNext = () => {
  emits('next')
}

const handleCheck = () => {
  emits('check')
}

const handleAllCheck = () => {
  emits('all-check')
}
const handleRetry = () => {
  console.log('retry')
}
</script>

<style scoped lang="scss">
.document-info-box {
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .file-info {
    display: flex;
    align-items: center;
    .doc-icon {
      display: block;
    }
    .file-icon {
      width: 20px;
      height: 20px;
      margin:0 4px 0 12px ;
    }
    .file-name {
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .file-details-box {
    display: flex;
    align-items: center;
    color: var(--text-4);
    border-radius: 6px;
    padding: 5px 16px;
    background: rgba(230, 239, 255, 0.7);
    .num {
      margin: 0 2px;
      &.error {
        color: var(--error-6);
      }
    }
    .btn {
      padding: 0;
      height: 22px;
      line-height: 22px;
      margin-left: 8px;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    .divider {
      font-size: 16px;
      padding: 0 8px;
      color: #D8D8D8;
    }
  } 
}
</style>
