<template>
  <BaseDrawer v-model="drawerVisible" :closable="false" width="calc(100% - 80px)"
              :mask-style="{background: 'transparent'}">
    <div class="knowledge-create-body">
      <header>
        <a-button shape="circle" :icon="h(LeftOutlined)" />
        <span class="title">创建知识库</span>
      </header>

      <a-divider />
      <div class="main">
        <KnowledgeForm />
        <div class="remark">说明</div>
      </div>
      <a-divider />

      <a-space>
        <a-button type="primary">确认创建</a-button>
        <a-button>取消</a-button>
      </a-space>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { h } from 'vue'
import { LeftOutlined } from '@ant-design/icons-vue'
import BaseDrawer from '@/components/BaseDrawer/index.vue'
import KnowledgeForm from '@/views/knowledge/knowledge-form.vue'

interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {})

const drawerVisible = computed(() => props.visible)
</script>

<style scoped lang="scss">
.knowledge-create-body {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 24px;

  :deep(.ant-btn > span) {
    display: inline-flex;
  }

  header {
    display: flex;
    gap: 12px;

    .title {
      font-family: SourceHanSansCN-Revision;
      font-size: 20px;
      line-height: 28px;
      color: #010207;
    }
  }

  .main {
    flex: 1;
    display: flex;
    gap: 40px;
    height: calc(100vh - 208px);

    form {
      width: 440px;
    }

    .remark {
      flex: 1;
      background: #ccc;
    }
  }
}
</style>
