<template>
  <div class="knowledge-group">
    <span>知识库群组</span>
    <a-tree :tree-data="treeData" v-model:expandedKeys="expandedKeys" show-icon>
      <template #icon="{children}">
        <SvgIcon v-if="children" icon="icon-folder1" class="file-icon" />
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getGroupTreeData } from '@/api/knowledge'

const treeData = ref()
const expandedKeys = ref<string[]>(['0', '01', '011', '012'])

const initData = async () => {
  let params = {}
  const { err, data } = await getGroupTreeData(params)
  if (err) return
  treeData.value = data.dataList
}

onMounted(() => {
  // treeData.value = TestTree
  initData()
})
</script>

<style scoped lang="scss">
.knowledge-group {
  width: 256px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px 0 0 0;

  :deep(.ant-tree-switcher) {
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-tree-switcher-icon {
      font-size: 14px;
    }
  }

  .file-icon {
    width: 16px;
    height: 24px;
  }
}
</style>
