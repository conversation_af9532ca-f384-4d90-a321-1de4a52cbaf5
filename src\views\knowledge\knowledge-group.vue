<template>
  <div class="knowledge-group">
    <span>知识库群组</span>
    <a-tree
      v-if="treeData.length" :tree-data="treeData"
      :field-names="{  key:'id',  title: 'name'}"
      @select="onSelect"
      defaultExpandAll show-icon
    >
      <template #icon="{children}">
        <SvgIcon v-if="children" icon="icon-folder1" class="file-icon" />
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getGroupTreeData } from '@/api/knowledge'

const emits = defineEmits(['update:selectedId'])

const treeData = ref([])

const initData = async () => {
  let params = {}
  const { err, data } = await getGroupTreeData(params)
  if (err) return
  treeData.value = data
}

onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.knowledge-group {
  width: 256px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px 0 0 0;

  :deep(.ant-tree-switcher) {
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-tree-switcher-icon {
      font-size: 14px;
    }
  }

  .file-icon {
    width: 16px;
    height: 24px;
  }
}
</style>
