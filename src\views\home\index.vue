<template>
  <div class="record">
    <div class="record-bar">
      <h3 class="banner-title">列表</h3>
    </div>
    <!-- 搜索 -->
    <a-form ref="formRef" class="search-form" :model="formState" layout="inline" @finish="handleFinish">
      <a-form-item label="项目名称：" name="projectName">
        <a-input v-model:value="formState.projectName" style="width: 200px" :maxlength="30" allow-clear />
      </a-form-item>
      <a-form-item label="评审状态：" name="status">
        <a-select v-model:value="formState.status" style="width: 150px" placeholder="评审状态" allow-clear>
          <a-select-option v-for="(value, key) of statusMap" :key="key" :value="key">{{ value }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="日期：" name="startTime">
        <a-date-picker v-model:value="formState.startTime" value-format="YYYY-MM-DD" />
      </a-form-item>
      <a-form-item label="" name="endTime">
        <a-date-picker v-model:value="formState.endTime" value-format="YYYY-MM-DD" />
      </a-form-item>
      <div class="search-form-btn">
        <a-button type="primary" html-type="submit">查询</a-button>
        <a-button style="margin-left: 10px" @click="resetForm">重置</a-button>
      </div>
    </a-form>
    <div class="toolbar"><a-button type="primary" @click="handleAdd()">新增</a-button></div>
    <div class="record-content">
      <a-spin v-if="state.loading" :spinning="state.loading"></a-spin>
      <template v-else>
        <a-table
v-if="state.list.length" class="table-box" :columns="tableColumns" :data-source="state.list"
          :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              {{ statusMap[record.status] }}
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-button type="primary" @click="handleEdit(record)">修改</a-button>
              <a-button type="primary" danger style="margin-left: 10px;" @click="handleDel(record)">删除</a-button>
            </template>
          </template>
        </a-table>
        <base-empty v-else height="50vh" />
      </template>
    </div>
    <a-pagination
    v-if="state.list.length"
    v-model:current="state.pages.pageNum"
    show-quick-jumper
    :total="state.pages.total" @change="onChange" />
  </div>
  <!-- 新增/修改弹框 -->
  <edit-modal v-model="modalVisible" :title="modalTitle" :record="modalFormState" @submit="handleModalSubmit" />

  <!-- 删除确认弹框 -->
  <a-modal
  v-model:open="deleteModalVisible"
  title="确认删除"
  @ok="handleDeleteConfirm"
  @cancel="deleteModalVisible = false">
    <p>确定要删除这条记录吗？</p>
  </a-modal>
</template>

<script setup lang='ts'>
import { reactive, ref } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import { statusMap, tableColumns } from './hooks/use-options'
import { getMultiRecordList } from '@/api/test'
import { useList } from '@/hooks/use-list'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import EditModal from './components/EditModal.vue'

defineOptions({
  name: 'HomeIndex'
})

const onChange = (pageNumber: number, pageSize: number) => {
  onPageChange(pageNumber)
  onSizeChange(pageNumber, pageSize)
}
// 查询
interface FormState {
  projectName: string;
  status: number | null;
  startTime: Dayjs;
  endTime: Dayjs;
}
const formRef = ref<FormInstance>();
const formState = reactive<FormState>({
  projectName: '',
  status: null,
  startTime: undefined as unknown as Dayjs,
  endTime: undefined as unknown as Dayjs
})
const handleFinish = () => {
  state.params = { ...formState }
  getList()
}
const resetForm = () => {
  formRef.value?.resetFields()
}
// 获取记录
const { state, onPageChange, getList, onSizeChange } = useList({
  params: formState,
  getList: getMultiRecordList
})
// getList()

// 新增/修改弹框相关逻辑
const modalVisible = ref(false)
const modalTitle = ref('新增')
const initRecord = {
  projectName: '',
  packageId: '',
  status: null,
  startTime: null
}
const modalFormState = reactive({})

const handleAdd = () => {
  modalTitle.value = '新增'
  Object.assign(modalFormState, initRecord)
  modalVisible.value = true
}

const handleEdit = (record: any) => {
  modalTitle.value = '修改'
  Object.assign(modalFormState, record)
  modalVisible.value = true
}

const handleModalSubmit = async (formData: any) => {
  try {
    if (modalTitle.value === '新增') {
      // await addRecord(formData) // 新增接口
    } else {
      // await updateRecord(formData) // 修改接口
    }
    modalVisible.value = false
    getList()
  } catch (error) {
    console.error(error)
  }
}

// 删除弹框相关逻辑
const deleteModalVisible = ref(false)
const currentRecord = ref<any>(null)

const handleDel = (record: any) => {
  currentRecord.value = record
  deleteModalVisible.value = true
}

const handleDeleteConfirm = async () => {
  try {
    //   await deleteRecord(currentRecord.value.id)   //删除接口
    deleteModalVisible.value = false
    getList()
  } catch (error) {
    console.error(error)
  }
}
</script>
<style lang="less" scoped>
.record {
  padding: 20px;
  box-sizing: border-box;

  .banner-title {
    color: #212227;
    font-weight: 600;
    font-size: 24px;
  }

  .record-bar {
    display: flex;
    justify-content: space-between;
  }

  .record-content {
    margin: 10px 0;
    text-align: center;
    width: 100%;
    min-height: 50vh;
  }

  .search-form {
    margin-top: 10px;
  }

  :deep(.table-box) {
    width: 100%;
  }

  :deep(.ant-spin) {
    width: 100%;
  }
}
</style>
