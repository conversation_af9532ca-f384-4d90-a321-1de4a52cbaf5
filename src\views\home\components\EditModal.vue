<template>
  <a-modal
    :open="visible"
    :title="title"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="项目名称" name="projectName">
        <a-input v-model:value="formState.projectName" />
      </a-form-item>
      <a-form-item label="包号" name="packageId">
        <a-input v-model:value="formState.packageId" />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-select v-model:value="formState.status">
          <a-select-option v-for="(value, key) in statusMap" :key="key" :value="key">{{ value }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="开始时间" name="startTime">
        <a-date-picker v-model:value="formState.startTime" value-format="YYYY-MM-DD" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref, watch , computed} from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { statusMap } from '../hooks/use-options'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  record: {
    type: Object,
    default: () => ({})
  }
})
const visible = computed({
  get: () => props.modelValue,
  set: (value:boolean) => {
    emit('update:modelValue', value)
  }
})
const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref<FormInstance>()
const formState = reactive({
  projectName: '',
  packageId: '',
  status: null,
  startTime: null
})

const formRules = {
  projectName: [{ required: true, message: '请输入项目名称' }],
  packageId: [{ required: true, message: '请输入包号' }],
  status: [{ required: true, message: '请选择状态' }],
  startTime: [{ required: true, message: '请选择开始时间' }]
}

watch(() => props.record, (newVal) => {
  Object.assign(formState, newVal)
}, { deep: true })

const handleOk = async () => {
  try {
    await formRef.value?.validateFields()
    emit('submit', formState)
  } catch (error) {
    console.error(error)
  }
}

const handleCancel = () => {
  emit('update:modelValue', false)
}
</script>