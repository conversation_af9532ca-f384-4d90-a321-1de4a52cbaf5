<template>
  <a-form :model="form" layout="vertical" :rules="rules">
    <a-form-item label="文件名称" :rules="rules.name">
      <a-input v-model:value="form.name" placeholder="请输入文件名称" :maxlength="100" />
    </a-form-item>

    <a-form-item label="开启状态" :rules="rules.isEnabled">
      <a-switch v-model:checked="form.isEnabled" />
    </a-form-item>

    <a-form-item label="标签" :rules="rules.tags">
      <a-tag v-for="(tag, index) in form.tags" :key="index" closable @close="removeTag(index)">{{ tag }}</a-tag>
      <a-tag style="background: #fff; border-style: dashed" @click="">
        <PlusOutlined />
        新标签
      </a-tag>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

const form = reactive({
  name: '政府采购框架协议采购方式管理暂行办法',
  isEnabled: true,
  tags: ['标签1', '标签2', '标签3']
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ],
  isEnabled: [
    { required: true, message: '请选择开启状态', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请至少添加一个标签', trigger: 'change' }
  ]
}

// 删除标签
const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}
</script>

<style scoped lang="scss">

</style>
