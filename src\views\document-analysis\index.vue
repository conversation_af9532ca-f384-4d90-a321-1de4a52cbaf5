<template>
  <div class="document-analysis">
    <div class="breadcrumb"><span class="text1">福建省采购案例库</span><span class="text1">/</span><span class="tex2">人工复核</span></div>
    <div class="steps-box">
      <a-steps v-model:current="currentStep" size="small" :items="stepsOptions"></a-steps>
    </div>
    <!-- 文档信息组件 -->
    <DocumentInfo
    @prev="handlePrev"
    @next="handleNext"
    @all-check="handleSave"
    />
    <div class="main-box">
      <div class="original-text">
        <div class="text-header">
          <span>原文对照</span>
          <a-button class="download-btn">下载文件</a-button>
        </div>
        <div class="text-content">
          <pdf-reader v-if="isPdf" :url="pdfSource" :page="currentPage" :rect="currentRect" :zoom="95"/>
          <KkfileReader v-else :url="pdfSource"/>
        </div>
      </div>
      <!-- 右侧切片区域 -->
      <div class="slice-box">
        <div class="slice-search">
          <span class="slice-count">切片 ({{ sliceData.length }})</span>
          <div class="search-body">
            <div class="search-input">
              <a-input
                v-model:value="searchKeyword"
                placeholder="请输入"
                allow-clear
                @input="handleSearch">
                <template #suffix>
                  <SearchOutlined />
                </template>
              </a-input>
            </div>
            <a-button  @click="handleAdd">新增</a-button>
          </div>
        </div>
        <div class="slice-body">
          <SlicePanel
          v-for="(item,index) in sliceData"
          :key="index"
          :item="item"
          :is-active="activeId === item.id"
          @edit="handleEdit"
          @copy="handleCopy"
          @detail="handleDetail"
          @delete="handleDelete"
          @click="handleActive(item)"/>
        </div>
      </div>
    </div>
    <div class="step-btns">
      <a-button  @click="handlePrev">上一步</a-button>
      <a-button type="primary"  @click="handleNext">下一步</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref ,computed, nextTick } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { stepsOptions } from '@/views/hooks/use-option'
import { getSlicePageList, getFileById } from '@/api/knowledge'
import PdfReader from '@/components/PdfReader/index.vue'
import KkfileReader from '@/components/KkfileReader/index.vue'
import SlicePanel from './components/slice-panel/index.vue'
import DocumentInfo from './components/document-info/index.vue'


defineOptions({
  name: 'DocumentAnalysis'
})
// pdf
const pdfSource = ref('')
const isPdf = computed(() => {
  if (!pdfSource.value) return false
  return pdfSource.value.toLowerCase().endsWith('.pdf')
})
const currentPage = ref(1)
const currentRect = ref([])
const getFile = async () => {
  const { err, data } = await getFileById('1868491406849388557')
  if (err) return
  pdfSource.value = data.fileUrl || ''
}

//  审核

const handlePrev = () => {
  console.log('上一个')
}

const handleNext = () => {
  console.log('下一个')
}

const handleSave = () => {
  console.log('保存修改')
} 
// 步骤
const currentStep = ref<number>(1);
// 搜索关键词
const searchKeyword = ref('')

const handleSearch = () => {
  console.log('搜索:', searchKeyword.value)
}

const handleAdd = () => {
  console.log('新增切片')
}
// 切片
const sliceData = ref<any>([])
const activeId = ref()
const sliceLoading = ref(false)
const getSliceData = async () => {
  if (sliceLoading.value) return
  sliceLoading.value = true
    const {err, data } = await getSlicePageList({
      fileId: '1868491406849388557',
      pageNum: 1,
      pageSize: 100
    })
    sliceLoading.value = false
    if (err) return
    sliceData.value = data.dataList || []
}
const handleActive = (item: Record<string, any>) => {
  activeId.value = item.id
  currentPage.value = -1
  if (item.page) {
    nextTick(() => {
      currentPage.value = Number(item.page)
    })
  }
}
const handleEdit = (info: any) => {
  console.log('编辑政策信息:', info)
}

const handleCopy = (info: any) => {
  console.log('复制政策信息:', info)
}

const handleDetail = (info: any) => {
  console.log('查看政策详情:', info)
}

const handleDelete = (clause: any) => {
  console.log('删除:', clause)
}

const init = async () => {
  await getFile()
  await getSliceData()
}
init()
</script>

<style scoped lang="scss">
.document-analysis {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .breadcrumb,
  .steps-box {
    flex-shrink: 0;
  }
  .breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .text1 {
      color: var(--text-3);
    }
    .text2{
      color: var(--text-5);
    }
  }
  .steps-box {
    padding: 24px 210px;
    height: 72px;
    background-color: var(--neutral-2);
    border-radius: 8px;
    margin-bottom: 16px;
  }
  .main-box {
    display: flex;
    border-radius: 8px;
    border:1px solid var(--neutral-3);
    flex:1;
    min-height: 0;
    .original-text {
      height: 100%;
      flex: 1;
      min-width: 0;
      border-right: 1px solid var(--neutral-3);
      .text-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        height: 72px;
        color: var(--text-5);
        font-size: 16px;
      }
      .text-content {
        padding:0 16px;
        height: calc(100% - 72px);
        overflow-y: auto;
      }
    }
    .slice-box {
      height: 100%;
      padding: 20px 20px 2px 20px;
      // width: 360px;
      width: 500px;
      display: flex;
      flex-direction: column;
      .slice-search {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        .slice-count {
          color: var(--text-5);
          font-size: 16px;
        }
        .search-body {
          display: flex;
          align-items: center;
          .search-input {
            margin-right: 8px;
          }
        }
      }
      .slice-body {
        flex: 1;
        min-height:0;
        overflow-y: auto;
      }
    }
  }
  .step-btns {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 25px;
  }
}
</style>
