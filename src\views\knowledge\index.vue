<template>
  <div class="knowledge">
    <div class="header">
      <span class="title">知识库</span>
      <span class="remark">用于存储和管理各类知识文档，实现多种数据源文档的上传、切片</span>
    </div>

    <div class="body">
      <div class="main">
        <KnowledgeGroup></KnowledgeGroup>
        <a-divider type="vertical" style="height: 100%" />
        <KnowledgeTable></KnowledgeTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import KnowledgeGroup from '@/views/knowledge/knowledge-group.vue'
import KnowledgeTable from '@/views/knowledge/knowledge-table.vue'
</script>

<style scoped lang="scss">
.knowledge {
  //width: calc(100vw - 80px);
  width: calc(100vw - 256px);
  height: calc(100vh - 24px);
  display: flex;
  flex-direction: column;
  padding: 24px 24px 0 24px;
  border-radius: 16px 0 0 16px;
  gap: 24px;

  .header {
    height: 62px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;

    .title {
      height: 32px;
      color: #010207;
      font-family: SourceHanSansCN-Revision;
      font-size: 24px;
      font-weight: bold;
      line-height: 32px;
    }

    .remark {
      height: 22px;
      font-family: Source Han Sans;
      font-size: 14px;
      line-height: 22px;
      color: #86909C;
    }
  }

  .body {
    flex: 1;
    display: flex;
    padding-top: 1px;
    background: #F2F3F5;

    .main {
      width: 100%;
      display: flex;
      gap: 16px;
      background: #FFFFFF;
    }
  }
}
</style>
