export const mockPolicyData = {
  // 四种不同类型的条款数据
  clauses: [
    {
      id: '5',
      type: '政策信息',
      indexNumber: '000014349/2016-00238',
      issuingOrgan: '国务院',
      documentNumber: '国发〔2016〕68号',
      publishDate: '2016年12月08日'
    },
    // 类型1：包含图片和标签的完整条款
    {
      id: '1',
      type: '自动切片1',
      title: '第一章 总则 / 第二条 / 更多层级展示起码出省略号... ',
      content:'在中华人民共和国境内进行的政府采购活动适用本法。本法所称政府采购，是指各级国家机关、事业单位和团体组织，使用财政性资金采购依法制定的集中采购目录以内的或者采购限额标准以上的货物、工程和服务的行为。',
      enabled: true,
      expanded: false,
      images: [
        { url: 'https://picsum.photos/100/100?random=1', alt: '政府采购文件' },
        { url: 'https://picsum.photos/100/100?random=2', alt: '招标公告' },
        { url: 'https://picsum.photos/100/100?random=3', alt: '合同文本' }
      ],
      tags: ['标签1', '标签2', '标签3']
    },

    // 类型2：只有标签，无图片
    {
      id: '2',
      type: '自动切片2',
      content: '第一章 总则 / 第三条 政府采购应当遵循公开透明原则、公平竞争原则、公正原则和诚实信用原则。',
      enabled: true,
      expanded: false,
      tags: ['标签1', '标签2', '标签3']
    },

    // 类型3：纯文本，无图片无标签
    {
      id: '3',
      type: '自动切片3',
      content: '第一章 总则 / 第四条 政府采购工程进行招标投标的，适用招标投标法。',
      enabled: true,
      expanded: false
    },

    // 类型4：长文本内容
    {
      id: '4',
      type: '自动切片4',
      content: '第二章 政府采购当事人 / 第五条 任何单位和个人不得采用任何方式，阻挠和限制供应商自由进入本地区和本行业的政府采购市场。第六条 政府采购应当严格按照批准的预算执行。第七条 政府采购实行集中采购和分散采购相结合。集中采购的范围由省级以上人民政府公布的集中采购目录确定。',
      enabled: false,
      expanded: false,
      images: [
        { url: 'https://picsum.photos/100/100?random=4', alt: '采购流程图' },
        { url: 'https://picsum.photos/100/100?random=5', alt: '预算文件' }
      ],
      tags: ['采购管理', '预算执行', '集中采购', '分散采购']
    }
  ]
}

// 生成更多测试数据的函数
export const generateMockClauses = (count: number = 10) => {
  const titles = [
    '总则条款', '采购原则', '当事人权利', '采购程序', '合同管理',
    '监督检查', '法律责任', '附则条款', '实施细则', '特殊规定'
  ]
  
  const contentTemplates = [
    '第{chapter}章 {section} / 第{article}条 政府采购活动应当遵循相关法律法规的规定，确保采购过程的公开、公平、公正。',
    '第{chapter}章 {section} / 第{article}条 采购人应当按照规定的程序和要求组织实施政府采购活动。',
    '第{chapter}章 {section} / 第{article}条 供应商参与政府采购活动应当具备相应的资格条件。',
    '第{chapter}章 {section} / 第{article}条 政府采购合同的履行应当严格按照合同约定执行。'
  ]

  const tagOptions = [
    ['法律条款', '基础规定', '总则'],
    ['采购流程', '程序规范', '操作指南'],
    ['资格审查', '供应商管理', '准入条件'],
    ['合同条款', '履约管理', '风险控制'],
    ['监督机制', '违规处理', '责任追究']
  ]

  return Array.from({ length: count }, (_, index) => {
    const hasImages = Math.random() > 0.6
    const hasTags = Math.random() > 0.3
    const chapter = Math.floor(index / 3) + 1
    const article = index + 1

    return {
      id: `clause_${index + 1}`,
      type: `${titles[index % titles.length]}${index + 1}`,
      title: `第${chapter}章 ${['总则', '采购管理', '合同履行', '监督检查'][index % 4]} / 第${article}条`,
      content: contentTemplates[index % contentTemplates.length]
        .replace('{chapter}', chapter.toString())
        .replace('{section}', ['总则', '采购管理', '合同履行', '监督检查'][index % 4])
        .replace('{article}', article.toString()),
      enabled: Math.random() > 0.2,
      expanded: false,
      ...(hasImages && {
        images: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, imgIndex) => ({
          url: `https://picsum.photos/100/100?random=${index * 10 + imgIndex}`,
          alt: `图片${imgIndex + 1}`
        }))
      }),
      ...(hasTags && {
        tags: tagOptions[index % tagOptions.length].slice(0, Math.floor(Math.random() * 3) + 1)
      })
    }
  })
}