import config from '@/config'
import { http } from '@/services/http'
/** 刷新验证码 */
export const verifyCodeUrl = config.api.baseUrl+ '/api/encrypt/public/v1/verifyCode?key='
/** 切片 */
export const getSlicePageList = (data: { fileId: string; pageNum: number; pageSize: number }) =>
http({
  url: '/knowledge/slice/v1/page',
  data,
  method: 'post'
})

/** 获取文件 */
export const getFileById = (fileId: string) =>
http({
  url: `/knowledge/file/v1/getById?fileId=${fileId}`,
  method: 'get'
})

/** 获取知识库群组树 */
export const getGroupTreeData = (
  data: { parentId?: string }
) => http({
  url: '/knowledge/userGroup/v1/getTreeList?parentId=' + data.parentId,
  method: 'post'
})

/** 分页查询知识库 */
export const knowledgePage = (
  data: {
    pageNum: number,
    pageSize: number,
    name?: string,
    type?: string,
    groupId?: string
  }
) => http({
  url: '/knowledge/knowledgeBase/v1/page',
  method: 'post',
  data
})
