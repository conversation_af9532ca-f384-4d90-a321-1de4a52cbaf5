<template>
  <div class="knowledge-table">
    <div class="search">
      <a-form :model="searchForm" layout="inline">
        <a-form-item>
          <a-select v-model:value="searchForm.type" @change="handleChange">
            <a-select-option value="all">全部类型</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-input-search v-model:value="searchForm.name" placeholder="搜索知识库名称" style="width: 200px" />
        </a-form-item>
      </a-form>

      <a-button type="primary" :icon="h(PlusOutlined)" @click="showCreateView">创建知识库</a-button>
    </div>

    <a-table :columns="columns" :data-source="tableData" :scroll="{ x:1600, y: 'calc(100vh - 302px)' }">
      <template #bodyCell="{ column, index, record, text }">
        <template v-if="column.dataIndex === 'name'">
          <div class="table-column-name">
            <span>{{ record.name }}</span>
            <span class="remark">{{ record.description }}</span>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'tags'">
<!--          <a-tag v-for="tag in record.tags" :key="tag.id">{{ tag.name }}</a-tag>-->
          <a-tag v-for="tag in record.tags" :key="tag.id">{{ tag }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'type'">
          <a-tag>{{ typeOptions[text] }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'isEnabled'">
          <a-switch :checked="text === 1" />
        </template>
        <template v-else-if="column.key === 'action'">
          <a-button type="link" @click="router.push({name: 'KnowledgeLawStore'})">查看</a-button>
          <a-button type="link" @click="doEdit">编辑</a-button>
          <a-popconfirm title="是否确定要删除该知识库？" ok-text="是" cancel-text="否" @confirm="deleteStore">
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>

    <KnowledgeCreate v-model:visible="visible" />

    <BaseDialog v-model="editorVisible" title="编辑知识库" width="520px">
      <KnowledgeForm />
    </BaseDialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import TestTable from '../../../public/test-table.json'
import { h } from 'vue'
import { useRouter } from 'vue-router'
import { PlusOutlined } from '@ant-design/icons-vue'
import KnowledgeCreate from '@/views/knowledge/knowledge-create.vue'
import { BaseDialog } from '@/components/BaseDialog'
import KnowledgeForm from '@/views/knowledge/knowledge-form.vue'
import { knowledgePage } from '@/api/knowledge'

const router = useRouter()
const visible = ref(false)
const editorVisible = ref(false)

const tableData = ref()

const typeOptions: Record<string, any> = {
  '1': '法律法规',
  '2': '流程图',
  '3': '负面清单',
  '4': '操作手册',
  '5': '国库司问答',
  '6': '指导性案例',
  '7': '监督处罚',
  '8': 'QA'
}

const columns = [
  { title: '知识库名称', dataIndex: 'name', width: 300, fixed: 'left' },
  { title: '知识库标签', dataIndex: 'tags', width: 200 },
  { title: '文件数量', dataIndex: 'fileCount' },
  { title: '类型', dataIndex: 'type' },
  { title: '开启状态', dataIndex: 'isEnabled' },
  { title: '创建人', dataIndex: 'creatorName' },
  { title: '创建时间', dataIndex: 'createTime', width: 200 },
  { title: '最后更新时间', dataIndex: 'updateTime', width: 200 },
  { title: '操作', key: 'action', width: 250, fixed: 'right' }
]

const searchForm = ref({
  type: 'all',
  name: ''
})

const showCreateView = () => {
  visible.value = true
}

const doEdit = () => {
  editorVisible.value = true
}

const deleteStore = () => {
  alert('操作成功')
}

const initData = async () => {
  const params = {
    pageNum: 1,
    pageSize: 10
  }
  const { err, data } = await knowledgePage(params)
  if (err) return
  tableData.value = data.dataList
}

onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.knowledge-table {
  position: relative;
  max-width: calc(100% - 300px);
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px 0 0 0;
  gap: 16px;

  :deep(.ant-btn > span) {
    display: inline-flex;
  }

  .search {
    display: flex;
    justify-content: space-between;

    :deep(.ant-select) {
      width: 200px;
    }
  }

  .table-column-name {
    display: flex;
    flex-direction: column;

    .remark {
      font-family: SourceHanSansCN-Revision;
      font-size: 12px;
      color: #86909C;
    }
  }
}
</style>
