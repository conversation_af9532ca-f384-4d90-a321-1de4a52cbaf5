// import dayjs from 'dayjs';

export const mockGetList = (params: any, body: any) => {
  const response: any = {
    success: true,
    code: 200,
    data: {
      items: new Array(50).fill(1).map((o, i) => ({
        id: i,
        name: `name_${i}`,
        nick_name: `nick_name_${i}`,
        // table content type
        price: `${i * 100 + i * 50}`,
        number: `${i * 100 + i * 10}`,
        percent: `${((50 + i) / 100).toFixed(2)}`,
        duration: `${60 * 10 * i} `,
        k1: `k1_${i}`,
        k2: `k2_${i}`,
        k3: `k3_${i}`,
        k4: `k4_${i}`,
        k5: `k5_${i}`,
        k6: `k6_${i}`,
        k7: `k7_${i}`,
        k8: `k8_${i}`,
        k9: `k9_${i}`,
        k10: `k10_${i}`,
        k11: `k11_${i}`,
        k12: `k12_${i}`,
        k13: `k13_${i}`,
        k14: `k14_${i}`,
        k15: `k15_${i}`
      })),
      pagination: { total_count: 1000 }
    }
  };
  return {
    err: null,
    data: response.data,
    response
  };
};
