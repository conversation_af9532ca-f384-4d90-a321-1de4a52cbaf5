<template>
  <a-form :model="formData" layout="vertical" class="knowledge-form">
    <a-form-item label="知识库名称" :required="true">
      <a-input v-model:value="formData.name" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="描述" :required="true">
      <a-input v-model:value="formData.description" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="所属群组" :required="true">
      <a-tree-select
        v-model:value="formData.group"
        placeholder="请选择"
        :tree-data="treeData"
        tree-default-expand-all
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        :label-in-value="true"
        @change="onchange"
      >
        <template #tagRender="{label}">{{ title || label }}</template>
      </a-tree-select>
    </a-form-item>
    <a-form-item label="类型" :required="true">
      <a-select v-model:value="formData.type" placeholder="请选择" :options="typeOptions" />
    </a-form-item>
    <a-form-item label="标签" :required="true">
      <a-select v-model:value="formData.tags" mode="multiple" placeholder="请选择" :options="tagOptions" />
    </a-form-item>
    <a-form-item label="切分策略" :required="true">
      <a-select v-model:value="formData.splitStrategy" placeholder="请选择" :options="splitStrategyOptions" />
    </a-form-item>
    <a-form-item label="向量模型" :required="true">
      <a-select v-model:value="formData.vectorModel" placeholder="请选择" :options="vectorModelOptions" />
    </a-form-item>
    <a-form-item label="增强策略" :required="true">
      <a-checkbox-group v-model:value="formData.enhancementStrategies" :options="strategyOptions" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'

interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {})

const title = ref()

const formData = reactive({
  name: '',
  description: '',
  group: undefined,
  type: undefined,
  tags: [],
  splitStrategy: '基于目录结构',
  vectorModel: 'bge-large',
  enhancementStrategies: ['questionGeneration']
})

// 选项数据
const strategyOptions = [
  { label: '问题生成', value: '1' },
  { label: '段落假要', value: '2' },
  { label: '三元组知识抽取', value: '3' }
]

const typeOptions = [
  { label: '文档库', value: 'document' },
  { label: '问答库', value: 'qa' },
  { label: '代码库', value: 'code' },
  { label: '图片库', value: 'image' }
]

const tagOptions = [
  { label: 'AI', value: 'ai' },
  { label: '机器学习', value: 'ml' },
  { label: '深度学习', value: 'dl' },
  { label: '自然语言处理', value: 'nlp' },
  { label: '计算机视觉', value: 'cv' },
  { label: '数据科学', value: 'ds' }
]

const splitStrategyOptions = [
  { label: '基于目录结构', value: '基于目录结构' },
  { label: '基于文件类型', value: '基于文件类型' },
  { label: '基于内容长度', value: '基于内容长度' },
  { label: '智能切分', value: '智能切分' }
]

const vectorModelOptions = [
  { label: 'bge-large', value: 'bge-large' },
  { label: 'bge-base', value: 'bge-base' },
  { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' },
  { label: 'm3e-large', value: 'm3e-large' },
  { label: 'm3e-base', value: 'm3e-base' }
]

const treeData = [
  {
    title: '政府采购', value: '0',
    children: [
      {
        title: '内蒙古自治区', value: '01',
        children: [
          { title: '呼和浩特市', value: '011' },
          { title: '包头市', value: '012' },
          { title: '乌海市', value: '013' },
          { title: '赤峰市', value: '014' },
          { title: '鄂尔多斯市', value: '015' }
        ]
      },
      {
        title: '福建省', value: '02',
        children: [
          { title: '国家级', value: '021' }
        ]
      }
    ]
  }
]

const buildTreePath = (value: string, treeData: any[], path: string[] = []): string[] | null => {
  for (let node of treeData) {
    const currentPath = [...path, node.title]
    if (node.value === value) {
      return currentPath
    }
    if (node.children) {
      const result = buildTreePath(value, node.children, currentPath)
      if (result) return result
    }
  }
  return null
}

const onchange = (value: any) => {
  const treePath = buildTreePath(value.value, treeData)
  if (treePath) {
    title.value = treePath.join(' / ')
    value.label = title.value
  }
}
</script>

<style scoped lang="scss">
.knowledge-form {
  overflow: auto;
  padding: 0 8px 0 0;
}
</style>
