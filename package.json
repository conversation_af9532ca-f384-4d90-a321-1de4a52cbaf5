{"name": "bosssoft-chat-ai", "version": "1.0.0", "private": false, "description": "博思软件", "author": "博思软件团队开发", "keywords": ["Bosssoft", "chatgpt", "chatbot", "vue"], "scripts": {"dev": "vite --mode dev", "test": "vite --mode test", "prod": "vite --mode prod", "build": "vite build", "build:test": "vite build --mode test", "build:demo": "vite build --mode demo", "build:prod": "vite build --mode prod", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "common:prepare": "husky install"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@ice/stark-app": "^1.5.0", "@ice/stark-data": "^0.1.3", "@vueuse/core": "^9.13.0", "ant-design-vue": "^4.0.7", "dayjs": "^1.11.13", "js-base64": "^3.7.7", "lottie-web": "^5.12.2", "pinia": "^2.0.33", "sass": "1.78.0", "terser": "^5.32.0", "uuid": "^9.0.0", "vite-plugin-index-html": "^2.0.2", "vite-plugin-top-level-await": "^1.4.1", "vue": "^3.5.12", "vue-eslint-parser": "^9.4.3", "vue-router": "^4.1.6", "vue3-cookies": "^1.0.6", "vue3-lottie": "^3.3.0", "vue3typed": "^0.1.5"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@babel/core": "^7.23.5", "@babel/eslint-parser": "^7.23.3", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@iconify/vue": "^4.1.0", "@types/crypto-js": "^4.1.1", "@types/node": "^18.14.6", "@types/uuid": "^9.0.7", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "axios": "^1.3.4", "crypto-js": "^4.1.1", "eslint": "^8.35.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "lint-staged": "^13.1.2", "markdown-it-link-attributes": "^4.0.1", "postcss": "^8.4.21", "prettier": "^3.1.0", "rimraf": "^4.2.0", "tailwindcss": "^3.2.7", "typescript": "~4.9.5", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.2", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.2.0"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}}