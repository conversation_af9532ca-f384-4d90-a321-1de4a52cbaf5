import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'
import config from '@/config'
export const dynamicRoutesMap: Record<string,any> = {
  AI_Chat: []
}
// 咨询路由
const aiNormalRoutes : RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Layout,
    redirect: { name: 'KnowledgeIndex',replace:true },
    children: [
    ]
  }
]
// 合规性审查路由
const complianceRoutes: RouteRecordRaw[] = [
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: Layout,
    redirect: { name: 'KnowledgeIndex' },
    children: [
      {
        path: 'index',
        name: 'KnowledgeIndex',
        meta: {
          title: '知识库列表'
        },
        component: () => import('@/views/knowledge/index.vue')
      },
      {
        path: 'law',
        name: 'KnowledgeLawStore',
        meta: {
          title: '文件库'
        },
        component: () => import('@/views/law-store/index.vue')
      },
      {
        path: 'analysis',
        name: 'KnowledgeDocumentAnalysis',
        meta: {
          title: '人工复核'
        },
        component: () => import('@/views/document-analysis/index.vue')
      }
    ]
  }
]
const demoRoutes: RouteRecordRaw[] = [
  // 专门放测试的
  {
    path: '/demo',
    name: 'demo',
    component: Layout,
    children: [
      {
        path: 'test',
        name: 'test',
        meta: {
          title: '测试'
        },
        component: () => import('@/views/demo/test/index.vue')
      }
    ]
  }
]
// 通用路由
const commonRoutes: RouteRecordRaw[] = [
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/agreement',
    name: 'Agreement',
    component: () => import('@/views/agreement/index.vue')
  },

  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404/index.vue')
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/views/exception/500/index.vue')
  }
]
// 开发环境路由
const devRoutes = config.env.VITE_ENV === 'dev' ? [...aiNormalRoutes,...complianceRoutes,...demoRoutes]: [...aiNormalRoutes,...complianceRoutes]
export const routes: RouteRecordRaw[] = [
  ...devRoutes,
  ...commonRoutes
]
export const fallbackRoutes = [
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/404'
  }
]
