<template>
  <div
    class="slice-card"
    :class="{ 'is-active': isActive }"
    @click="onClick"
  >
    <span class="slice-type">{{ item.type ||`自动切片${item.sortId}` }} </span>
    <div class="slice-action">
      <a-switch v-model:checked="enabled" size="small" />
      <div class="divider"></div>
      <EditOutlined class="icon"  @click="onEdit"/>
      <DeleteOutlined class="icon" @click="onCopy"/>
      <div class="divider"></div>
      <a-button class="btn-detail" type="text" size="small" @click="onDetail">
        详情
      </a-button>
    </div>
    <div class="slice-detail">
      <template v-if="item.indexNumber">
        <div class="detail-item">
          <span class="label">索引号：</span>
          <span class="value">{{ item.indexNumber }}</span>
        </div>
        <div class="detail-item">
          <span class="label">发文机关：</span>
          <span class="value">{{ item.issuingOrgan }}</span>
        </div>
        <div class="detail-item">
          <span class="label">发文字号：</span>
          <span class="value">{{ item.documentNumber }}</span>
        </div>
        <div class="detail-item">
          <span class="label">发布日期：</span>
          <span class="value">{{ item.publishDate }}</span>
        </div>
      </template>
      <div v-if="item.chapter || item.article" class="detail-title">
        <div v-if="item.chapter" class="detail-title-cell">
          <span class="value">{{ item.chapter }}</span>
        </div>
        <div v-if="item.article" class="detail-title-cell">
          <span class="line">/</span><span class="value">{{ item.article }}</span>
        </div>
      </div>
      <div v-if="item.text" class="detail-content">{{ item.text }}</div>
    </div>
    <div v-if="item.images" class="slice-image-box">
      <a-image v-for="(img,index) in item.images" :key="index" class="img" :src="img.url" />
    </div>
    <a-tag
      v-for="tag in item?.tags"
      :key="tag"
      class="slice-tag"
    >
      {{ tag }}
    </a-tag>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { message } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'

defineOptions({ name: 'SliceCard' })

interface Props {
  item: Record<string, any>
  isActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
})

const emits = defineEmits(['edit', 'copy', 'detail', 'onActive','editEnable'])

const enabled = computed({
  get() {
    return props.item.enabled
  },
  set(value) {
    emits('editEnable', { ...props.item, enabled: value })
  }
})

// 政策信息操作
const onEdit = () => {
  emits('edit', props.item)
}

const onCopy = () => {
  emits('copy', props.item)
  message.success('已复制到剪贴板')
}

const onDetail = () => {
  emits('detail', props.item)
}

const onClick = () => {
  emits('onActive', props.item)
}
</script>

<style scoped lang="scss">
.slice-card {
  position: relative;
  padding:12px 16px;
  margin-bottom: 12px;
  background: var(--fill-0);
  border:1px solid var(--neutral-3);
  border-radius: 8px;
  cursor: pointer;
  &.is-active,
  &:hover {
    border-color: var(--main-5);
    background: rgba(230, 239, 255, 0.7);
    .slice-type {
      background: var(--main-5);
      color: var(--fill-0);
    }
  }
  .slice-type {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    background: var(--main-1);
    color: var(--main-6);
    padding: 4px 12px;
    border-radius: 8px 0 8px 0;
    font-size: 12px;
  }
  .slice-action {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 28px;
    gap: 12px;
    .divider {
      width: 1px;
      height: 12px;
      background-color: var(--neutral-4);
      margin: 0 4px;
    }
    .icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      color:  var(--text-4);
    }
    .btn-detail {
      padding: 0;
      color: var(--main-6);
    }
  }

  .slice-detail {
    .detail-item {
      display: flex;
      font-size: 13px;
      line-height: 22px;
      .label {
        text-align: right;
        min-width: 65px;
      }
      .label,.value {
        color: var(--text-4);
      }
    }
    .detail-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
      color: var(--text-5);
      margin-bottom: 4px;
    }
    .detail-content {
      font-size: 13px;
      line-height: 22px;
      color: var(--text-4);
      white-space: break-spaces;
    }
    .detail-title-cell {
      display: flex;
      align-items: center;
      .line {
        color: var(--text-3);
        padding: 0 4px;
      }
    }
  }
  .slice-tag {
    margin-top: 13px;
  }
  .slice-image-box {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    :deep(.ant-image) {
      img {
        width: auto;
        height: 60px;
        border-radius: 4px;
      }
    }
  }
}
</style>
