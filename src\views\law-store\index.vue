<template>
  <div class="law-store">
    <header>
      <a-button shape="circle" :icon="h(LeftOutlined)" />
      <span class="title">国家法律法规库</span>
    </header>

    <a-divider />

    <div class="body">
      <div class="search">
        <a-form :model="searchForm" layout="inline">
          <a-form-item>
            <a-input-search v-model:value="searchForm.name" placeholder="搜索知识库名称" class="search-name" />
          </a-form-item>
        </a-form>

        <a-space>
          <a-popconfirm title="确定要批量删除吗？" ok-text="是" cancel-text="否" @confirm=""
                        :get-popup-container="(node:any) => {return node.parentNode}">
            <a-button danger>删除</a-button>
          </a-popconfirm>
          <a-button>导出</a-button>
          <a-button type="primary" :icon="h(DownloadOutlined)">导入文件</a-button>
        </a-space>
      </div>

      <a-table :columns="columns" :data-source="tableData"
               :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: doChange }"
               :scroll="{ x: 1800, y: 'calc(100vh - 276px)' }"
      >
        <template #bodyCell="{ column, index, record, text }">
          <template v-if="column.dataIndex === 'name'">
            <div class="table-column-name">
              <svg-icon :icon="getFileIcon()" class="file-icon" />
              <span>{{ text }}</span>
            </div>
          </template>

          <template v-else-if="column.dataIndex === 'state'">
            <a-tag :color="stateMap[text].color">{{ stateMap[text].title }}</a-tag>
          </template>

          <template v-else-if="column.dataIndex === 'checkState'">
            <a-tag>通过</a-tag>
          </template>

          <template v-else-if="column.dataIndex === 'open'">
            <a-switch :checked="text" />
          </template>

          <template v-else-if="column.dataIndex === 'tags'">
            <a-tag v-for="tag in record.tags" :key="tag.id">{{ tag.name }}</a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="router.push({name: 'KnowledgeDocumentAnalysis'})">查看切片</a-button>
              <a-button type="link">重试</a-button>
              <a-button type="link" @click="doEdit">编辑</a-button>

              <a-popconfirm title="是否确定要删除此知识文档？删除后，如需再次使用，请重新上传本文档。" ok-text="是" cancel-text="否" @confirm="">
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <BaseDialog v-model="editorVisible" title="编辑文件" width="small">
      <Editor />
    </BaseDialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { h } from 'vue'
import type { TableColumnsType } from 'ant-design-vue'
import { LeftOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import TestLawStoreTable from '../../../public/test-law-store-table.json'
import { getFileIcon } from '@/views/hooks/use-tool'
import { useRouter } from 'vue-router'
import KnowledgeForm from '@/views/knowledge/knowledge-form.vue'
import { BaseDialog } from '@/components/BaseDialog'
import Editor from '@/views/law-store/editor.vue'

const router = useRouter()
const tableData = ref()
const selectedRowKeys = ref()
const editorVisible = ref(false)

const searchForm = ref({
  name: ''
})

const stateMap: Record<string, any> = {
  success: { color: 'green', title: '解析成功' },
  error: { color: 'red', title: '解析失败' },
  doing: { color: 'blue', title: '解析中' },
  waiting: { color: '', title: '排队中' }
}

const columns: TableColumnsType = [
  { title: '文件名称', dataIndex: 'name', width: 300, fixed: 'left' },
  { title: '引用次数', dataIndex: 'count', width: 100 },
  { title: '解析状态', dataIndex: 'state', width: 100 },
  { title: '复核状态', dataIndex: 'checkState', width: 100 },
  { title: '开启状态', dataIndex: 'open', width: 100 },
  { title: '文件标签', dataIndex: 'tags' },
  { title: '创建时间', dataIndex: 'createTime', width: 200 },
  { title: '更新时间', dataIndex: 'updateTime', width: 200 },
  { title: '更新人', dataIndex: 'updator', width: 100 },
  { title: '操作', key: 'action', width: 300, fixed: 'right' }
]

const doChange = (keys: any[]) => {
  console.log(keys)
  selectedRowKeys.value = keys
}

const doEdit = () => {
  editorVisible.value = true
}

onMounted(() => {
  tableData.value = TestLawStoreTable
})
</script>

<style scoped lang="scss">
.law-store {
  width: calc(100vw - 80px);
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 24px;

  :deep(.ant-btn > span) {
    display: inline-flex;
  }

  header {
    display: flex;
    gap: 12px;

    .title {
      font-family: SourceHanSansCN-Revision;
      font-size: 20px;
      line-height: 28px;
      color: #010207;
    }
  }

  .body {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .search {
      display: flex;
      justify-content: space-between;

      .search-name {
        width: 280px;
      }
    }

    .table-column-name {
      display: flex;
      gap: 4px;

      .file-icon {
        width: 20px;
        height: 20px;
      }

      span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
